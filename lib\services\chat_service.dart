import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:mr_garments_mobile/models/chat_user.dart';
import 'package:mr_garments_mobile/models/chat.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/models/group.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/services/user_service.dart';

class ChatService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // Collection references
  static final CollectionReference _usersCollection = _firestore.collection(
    'users',
  );
  static final CollectionReference _chatsCollection = _firestore.collection(
    'chats',
  );
  static final CollectionReference _groupsCollection = _firestore.collection(
    'groups',
  );

  // ==================== USER MANAGEMENT ====================

  /// Create or update user in Firestore
  static Future<void> createOrUpdateUser(ChatUser user) async {
    try {
      await _usersCollection
          .doc(user.id)
          .set(user.toMap(), SetOptions(merge: true));
    } catch (e) {
      throw Exception('Failed to create/update user: $e');
    }
  }

  /// Sync current user with Firebase from Laravel backend data
  static Future<void> syncCurrentUserWithFirebase() async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();
      final currentUserEmail = await SessionService.getUserEmail();
      final currentUserRole = await SessionService.getUserRole();

      if (currentUserId != null &&
          currentUserName != null &&
          currentUserEmail != null) {
        // Ensure Firebase Auth user exists with the Laravel user ID as UID
        await _ensureFirebaseAuthWithLaravelId(currentUserId.toString());

        // Get existing user data to preserve FCM token and other fields
        final existingUser = await getUser(currentUserId.toString());

        final chatUser = ChatUser(
          id: currentUserId.toString(),
          name: currentUserName,
          email: currentUserEmail,
          role: currentUserRole ?? 'user',
          fcmToken:
              existingUser
                  ?.fcmToken, // Preserve existing FCM token (don't set default value)
          isOnline: existingUser?.isOnline ?? false, // Preserve online status
          lastSeen: existingUser?.lastSeen, // Preserve last seen
          mobile: existingUser?.mobile, // Preserve mobile
          profileImageUrl:
              existingUser?.profileImageUrl, // Preserve profile image
          createdAt: existingUser?.createdAt ?? DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await createOrUpdateUser(chatUser);
      }
    } catch (e) {
      // Silently handle sync errors
      debugPrint('❌ Error in syncCurrentUserWithFirebase: $e');
    }
  }

  /// Sync all Laravel users with Firebase (for admin use)
  static Future<void> syncAllUsersWithFirebase() async {
    try {
      final apiUsers = await UserService.fetchAllUsers();

      for (final userData in apiUsers) {
        try {
          final userId = userData['id'].toString();

          // Get existing user data to preserve FCM token and other fields
          final existingUser = await getUser(userId);

          final chatUser = ChatUser(
            id: userId,
            name: userData['name'] ?? 'Unknown User',
            email: userData['email'] ?? '',
            role: userData['account_type']?.toString().toLowerCase() ?? 'user',
            profileImageUrl:
                userData['profile_image_url'] ?? existingUser?.profileImageUrl,
            fcmToken: existingUser?.fcmToken, // Preserve existing FCM token
            isOnline: existingUser?.isOnline ?? false, // Preserve online status
            lastSeen: existingUser?.lastSeen, // Preserve last seen
            mobile:
                userData['mobile'] ?? existingUser?.mobile, // Preserve mobile
            createdAt: existingUser?.createdAt ?? DateTime.now(),
            updatedAt: DateTime.now(),
          );

          await createOrUpdateUser(chatUser);
        } catch (e) {
          continue;
        }
      }
    } catch (e) {
      // Silently handle sync errors
      debugPrint('❌ Error in syncAllUsersWithFirebase: $e');
    }
  }

  /// Get user by ID
  static Future<ChatUser?> getUser(String userId) async {
    try {
      final doc = await _usersCollection.doc(userId).get();
      if (doc.exists) {
        return ChatUser.fromDocument(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user: $e');
    }
  }

  /// Get all users with role-based filtering (for member selection)
  static Future<List<ChatUser>> getAllUsers() async {
    try {
      // Check if user is logged in first
      final isLoggedIn = await SessionService.isLoggedIn();
      if (!isLoggedIn) {
        return [];
      }

      final currentUserId = await SessionService.getUserId();
      final currentUserRole = await SessionService.getUserRole();

      if (currentUserId == null || currentUserRole == null) {
        return [];
      }

      // Fetch users from Laravel API
      final apiUsers = await UserService.fetchAllUsers();

      if (apiUsers.isEmpty) {
        return []; // Return empty list if no users found
      }

      List<ChatUser> users = [];
      for (final userData in apiUsers) {
        try {
          // Convert Laravel API user data to ChatUser
          final chatUser = ChatUser(
            id: userData['id'].toString(),
            name: userData['name'] ?? 'Unknown User',
            email: userData['email'] ?? '',
            role: userData['role']?.toString().toLowerCase() ?? 'user',
            profileImageUrl: userData['profile_image_url'],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
          users.add(chatUser);
        } catch (e) {
          continue;
        }
      }

      // Apply role-based filtering
      if (currentUserRole == 'admin' || currentUserRole == 'salesperson') {
        // Admin and SalesPerson can see all users except themselves
        users =
            users.where((user) => user.id != currentUserId.toString()).toList();
      } else {
        // Non-admin users can only see admin and salesperson users
        users =
            users
                .where(
                  (user) => user.role == 'admin' || user.role == 'salesperson',
                )
                .toList();
      }

      return users;
    } catch (e) {
      throw Exception('Failed to get all users: $e');
    }
  }

  /// Search users by name or email with role-based filtering
  static Future<List<ChatUser>> searchUsers(String query) async {
    try {
      // Check if user is logged in first
      final isLoggedIn = await SessionService.isLoggedIn();
      if (!isLoggedIn) {
        return [];
      }

      final currentUserId = await SessionService.getUserId();
      final currentUserRole = await SessionService.getUserRole();

      if (currentUserId == null || currentUserRole == null) {
        return [];
      }

      // Fetch users from Laravel API and filter by search query
      final apiUsers = await UserService.fetchAllUsers();

      List<ChatUser> users = [];
      for (final userData in apiUsers) {
        try {
          // Convert Laravel API user data to ChatUser
          final chatUser = ChatUser(
            id: userData['id'].toString(),
            name: userData['name'] ?? 'Unknown User',
            email: userData['email'] ?? '',
            role: userData['account_type']?.toString().toLowerCase() ?? 'user',
            profileImageUrl: userData['profile_image_url'],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          // Filter by search query (name or email)
          final name = chatUser.name.toLowerCase();
          final email = chatUser.email.toLowerCase();
          final searchQuery = query.toLowerCase();

          if (name.contains(searchQuery) || email.contains(searchQuery)) {
            users.add(chatUser);
          }
        } catch (e) {
          continue;
        }
      }

      // Apply role-based filtering
      if (currentUserRole == 'admin') {
        // Admin can search all users except themselves
        users =
            users.where((user) => user.id != currentUserId.toString()).toList();
      } else {
        // Non-admin users can only search admin users
        users = users.where((user) => user.role == 'admin').toList();
      }

      return users;
    } catch (e) {
      throw Exception('Failed to search users: $e');
    }
  }

  /// Update user online status
  static Future<void> updateUserOnlineStatus(
    String userId,
    bool isOnline,
  ) async {
    try {
      await _usersCollection.doc(userId).update({
        'isOnline': isOnline,
        'lastSeen': DateTime.now().millisecondsSinceEpoch,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      throw Exception('Failed to update online status: $e');
    }
  }

  /// Get user stream for real-time updates (including online status)
  static Stream<ChatUser?> getUserStream(String userId) {
    return _usersCollection.doc(userId).snapshots().map((doc) {
      if (doc.exists) {
        return ChatUser.fromDocument(doc);
      }
      return null;
    });
  }

  /// Update FCM token in Firestore (Cloud Functions will read from here)
  static Future<void> updateFCMToken(String laravelUserId, String token) async {
    try {
      debugPrint('🔄 Updating FCM token for user $laravelUserId');

      // Update in Firestore - Cloud Functions will read FCM tokens from here
      await FirebaseFirestore.instance
          .collection('users')
          .doc(laravelUserId) // use "92", not Firebase UID
          .update({
            'fcmToken': token,
            'updatedAt': DateTime.now().millisecondsSinceEpoch,
          });

      debugPrint('✅ FCM token updated in Firestore successfully');
      debugPrint('📱 User ID: $laravelUserId');
      debugPrint('🔑 FCM Token: ${token.substring(0, 20)}...');
    } catch (e) {
      debugPrint('❌ Failed to update FCM token: $e');
      throw Exception('Failed to update FCM token: $e');
    }
  }

  // ==================== CHAT MANAGEMENT ====================

  /// Create or get individual chat (admin can chat with anyone, others can only chat with admin)
  static Future<String> createIndividualChat(String otherUserId) async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserRole = await SessionService.getUserRole();

      if (currentUserId == null) throw Exception('User not logged in');

      // Ensure Firebase Auth is properly set up
      await _ensureFirebaseAuth();

      // Generate chat ID by combining user IDs
      final chatId = _generateChatId(currentUserId.toString(), otherUserId);

      // Check if chat already exists
      final existingChat = await _chatsCollection.doc(chatId).get();
      if (existingChat.exists) {
        return chatId;
      }

      // Ensure current user is synced to Firebase
      await syncCurrentUserWithFirebase();

      // Get user details - try Firebase first, then Laravel backend
      ChatUser? currentUser = await getUser(currentUserId.toString());
      ChatUser? otherUser = await getUser(otherUserId);

      // If other user not found in Firebase, try to get from Laravel backend
      if (otherUser == null) {
        try {
          final apiUsers = await UserService.fetchAllUsers();
          final userData = apiUsers.firstWhere(
            (user) => user['id'].toString() == otherUserId,
            orElse: () => null,
          );

          if (userData != null) {
            otherUser = ChatUser(
              id: userData['id'].toString(),
              name: userData['name'] ?? 'Unknown User',
              email: userData['email'] ?? '',
              role:
                  userData['account_type']?.toString().toLowerCase() ?? 'user',
              profileImageUrl: userData['profile_image_url'],
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            );

            // Sync to Firebase
            await createOrUpdateUser(otherUser);
          }
        } catch (e) {
          // Silently handle fetch errors
        }
      }

      if (currentUser == null || otherUser == null) {
        throw Exception('User not found');
      }

      // Validate chat creation permissions
      // Admin can chat with anyone, non-admin can only chat with admin
      if (currentUserRole != 'admin' && otherUser.role != 'admin') {
        throw Exception('Only admin can initiate chats with non-admin users');
      }

      // Create new chat
      final chat = Chat(
        id: chatId,
        type: ChatType.individual,
        memberIds: [currentUserId.toString(), otherUserId],
        memberNames: {
          currentUserId.toString(): currentUser.name,
          otherUserId: otherUser.name,
        },
        memberProfileUrls: {
          currentUserId.toString(): currentUser.profileImageUrl,
          otherUserId: otherUser.profileImageUrl,
        },
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _chatsCollection.doc(chatId).set(chat.toMap());
      return chatId;
    } catch (e) {
      throw Exception('Failed to create individual chat: $e');
    }
  }

  /// Get user's chats stream with role-based filtering
  static Stream<List<Chat>> getUserChatsStream(String userId) async* {
    try {
      // Check if user is logged in first
      final isLoggedIn = await SessionService.isLoggedIn();
      if (!isLoggedIn) {
        yield [];
        return;
      }

      // Get current user's role
      final userRole = await SessionService.getUserRole();
      if (userRole == null) {
        yield [];
        return;
      }

      // Get all chats for the user
      final chatsStream = _chatsCollection
          .where('memberIds', arrayContains: userId)
          .where('isActive', isEqualTo: true)
          .orderBy('updatedAt', descending: true)
          .snapshots()
          .map(
            (snapshot) =>
                snapshot.docs.map((doc) => Chat.fromDocument(doc)).toList(),
          );

      // Cache for user roles to avoid repeated database calls
      final Map<String, String?> userRoleCache = {};

      await for (final chats in chatsStream) {
        if (userRole == 'admin' || userRole == 'salesperson') {
          // Admin and SalesPerson can see all chats except broadcast individual chats they created
          final filteredChats =
              chats.where((chat) {
                // Filter out broadcast chats where admin/salesperson is the sender
                final chatData = chat.toMap();
                final isBroadcastChat = chatData['isBroadcastChat'] == true;
                final broadcastSenderId = chatData['broadcastSenderId'];

                if (isBroadcastChat && broadcastSenderId == userId) {
                  return false; // Hide broadcast chats created by this admin/salesperson
                }
                return true;
              }).toList();
          yield filteredChats;
        } else {
          // Non-admin users (manufacturer, distributor, retailer) can only see chats with admin and salesperson
          final filteredChats = <Chat>[];

          for (final chat in chats) {
            // Check if any of the other members in the chat is an admin or salesperson
            final otherMemberIds =
                chat.memberIds.where((id) => id != userId).toList();

            bool hasAdminOrSalespersonMember = false;
            for (final memberId in otherMemberIds) {
              // Check cache first
              String? memberRole = userRoleCache[memberId];
              if (memberRole == null) {
                final member = await getUser(memberId);
                memberRole = member?.role;
                userRoleCache[memberId] = memberRole;
              }

              if (memberRole == 'admin' || memberRole == 'salesperson') {
                hasAdminOrSalespersonMember = true;
                break;
              }
            }

            if (hasAdminOrSalespersonMember) {
              filteredChats.add(chat);
            }
          }

          yield filteredChats;
        }
      }
    } catch (e) {
      yield [];
    }
  }

  /// Get specific chat
  static Future<Chat?> getChat(String chatId) async {
    try {
      final doc = await _chatsCollection.doc(chatId).get();
      if (doc.exists) {
        return Chat.fromDocument(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get chat: $e');
    }
  }

  // ==================== MESSAGE MANAGEMENT ====================

  /// Send text message
  static Future<void> sendTextMessage({
    required String chatId,
    required String text,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
  }) async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();
      final currentUserRole = await SessionService.getUserRole();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      // Check if this is a group chat and user is admin
      final chatDoc = await _chatsCollection.doc(chatId).get();
      if (chatDoc.exists) {
        final chatData = chatDoc.data() as Map<String, dynamic>;
        final chatType = chatData['type'] ?? 'individual';

        // If it's a group chat and user is admin, use broadcast messaging
        if (chatType == 'group' && currentUserRole == 'admin') {
          await _sendBroadcastMessage(
            chatId: chatId,
            text: text,
            replyToMessageId: replyToMessageId,
            replyToText: replyToText,
            replyToSenderName: replyToSenderName,
          );
          return;
        }
      }

      // Regular individual chat message
      final messageId = _generateMessageId();
      final message = Message(
        id: messageId,
        senderId: currentUserId.toString(),
        senderName: currentUserName,
        type: MessageType.text,
        text: text,
        timestamp: DateTime.now(),
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
        status: MessageStatus.sending,
      );

      // Add message to chat's messages subcollection
      await _chatsCollection
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .set(message.toMap());

      // Update message status to sent
      await _updateMessageStatus(chatId, messageId, MessageStatus.sent);

      // Update chat's last message
      await _updateChatLastMessage(
        chatId,
        message.copyWith(status: MessageStatus.sent),
      );

      // Update unreadCounts for all other members
      final chatDocForUnread = await _chatsCollection.doc(chatId).get();
      if (chatDocForUnread.exists) {
        final chatDataForUnread =
            chatDocForUnread.data() as Map<String, dynamic>;
        final memberIds = List<String>.from(
          chatDataForUnread['memberIds'] ?? [],
        );
        final unreadCounts = Map<String, int>.from(
          chatDataForUnread['unreadCounts'] ?? {},
        );
        for (final memberId in memberIds) {
          if (memberId != currentUserId.toString()) {
            unreadCounts[memberId] = (unreadCounts[memberId] ?? 0) + 1;
          }
        }
        await _chatsCollection.doc(chatId).update({
          'unreadCounts': unreadCounts,
        });

        // Send push notifications to other members
        await _sendNotificationToMembers(
          memberIds:
              memberIds.where((id) => id != currentUserId.toString()).toList(),
          chatId: chatId,
          senderName: currentUserName,
          messageText: text,
          messageType: 'text',
        );
      }
    } catch (e) {
      throw Exception('Failed to send text message: $e');
    }
  }

  /// Create broadcast individual chat (only visible to recipient, not admin)
  // static Future<String> _createBroadcastIndividualChat(
  //   String recipientUserId,
  // ) async {
  //   try {
  //     final currentUserId = await SessionService.getUserId();
  //     final currentUserName = await SessionService.getUserName();

  //     if (currentUserId == null || currentUserName == null) {
  //       throw Exception('User not logged in');
  //     }

  //     // Generate chat ID by combining user IDs (same format as regular individual chats)
  //     final chatId = _generateChatId(currentUserId.toString(), recipientUserId);

  //     // Check if chat already exists
  //     final existingChat = await _chatsCollection.doc(chatId).get();
  //     if (existingChat.exists) {
  //       return chatId;
  //     }

  //     // Get recipient user data
  //     final recipientUser = await getUser(recipientUserId);
  //     if (recipientUser == null) {
  //       throw Exception('Recipient user not found');
  //     }

  //     // Create broadcast individual chat - only visible to recipient
  //     final chatData = {
  //       'id': chatId,
  //       'type': 'individual',
  //       'memberIds': [currentUserId.toString(), recipientUserId],
  //       'memberNames': {
  //         currentUserId.toString(): currentUserName,
  //         recipientUserId: recipientUser.name,
  //       },
  //       'memberProfileUrls': {
  //         currentUserId.toString(): '',
  //         recipientUserId: recipientUser.profileImageUrl,
  //       },
  //       'isActive': true,
  //       'lastMessage': null,
  //       'lastMessageTime': null,
  //       'lastMessageSenderId': '',
  //       'unreadCounts': {currentUserId.toString(): 0, recipientUserId: 0},
  //       'createdAt': DateTime.now().millisecondsSinceEpoch,
  //       'updatedAt': DateTime.now().millisecondsSinceEpoch,
  //       'isBroadcastChat': true, // Special flag to identify broadcast chats
  //       'broadcastSenderId':
  //           currentUserId
  //               .toString(), // ID of the admin who created this broadcast chat
  //     };

  //     await _chatsCollection.doc(chatId).set(chatData);
  //     return chatId;
  //   } catch (e) {
  //     throw Exception('Failed to create broadcast individual chat: $e');
  //   }
  // }

  /// Get or create individual chat for broadcast (reuses existing chats)
  static Future<String> _getOrCreateIndividualChatForBroadcast(
    String memberId,
  ) async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      // Generate consistent chat ID
      final chatId = _generateChatId(currentUserId.toString(), memberId);

      // Check if chat already exists
      final existingChat = await _chatsCollection.doc(chatId).get();
      if (existingChat.exists) {
        return chatId; // Return existing chat ID
      }

      // If chat doesn't exist, create it
      // Get member user data
      ChatUser? memberUser = await getUser(memberId);

      // If member not found in Firebase, try to get from Laravel backend
      if (memberUser == null) {
        try {
          final apiUsers = await UserService.fetchAllUsers();
          final userData = apiUsers.firstWhere(
            (user) => user['id'].toString() == memberId,
            orElse: () => null,
          );

          if (userData != null) {
            memberUser = ChatUser(
              id: userData['id'].toString(),
              name: userData['name'] ?? 'Unknown User',
              email: userData['email'] ?? '',
              role:
                  userData['account_type']?.toString().toLowerCase() ?? 'user',
              profileImageUrl: userData['profile_image_url'],
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            );

            // Sync to Firebase
            await createOrUpdateUser(memberUser);
          }
        } catch (e) {
          // Silently handle fetch errors
        }
      }

      if (memberUser == null) {
        throw Exception('Member user not found');
      }

      // Get current user data
      ChatUser? currentUser = await getUser(currentUserId.toString());
      if (currentUser == null) {
        // Sync current user if not found
        await syncCurrentUserWithFirebase();
        currentUser = await getUser(currentUserId.toString());
      }

      if (currentUser == null) {
        throw Exception('Current user not found');
      }

      // Create new individual chat
      final chat = Chat(
        id: chatId,
        type: ChatType.individual,
        memberIds: [currentUserId.toString(), memberId],
        memberNames: {
          currentUserId.toString(): currentUser.name,
          memberId: memberUser.name,
        },
        memberProfileUrls: {
          currentUserId.toString(): currentUser.profileImageUrl,
          memberId: memberUser.profileImageUrl,
        },
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _chatsCollection.doc(chatId).set(chat.toMap());
      return chatId;
    } catch (e) {
      throw Exception(
        'Failed to get or create individual chat for broadcast: $e',
      );
    }
  }

  /// Send broadcast message to group members as individual messages
  static Future<void> _sendBroadcastMessage({
    required String chatId,
    required String text,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
  }) async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      // First, add the message to the group chat immediately for admin to see
      final groupMessageId = _generateMessageId();
      final groupMessage = Message(
        id: groupMessageId,
        senderId: currentUserId.toString(),
        senderName: currentUserName,
        type: MessageType.text,
        text: text,
        timestamp: DateTime.now(),
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
        status: MessageStatus.sent,
      );

      await _chatsCollection
          .doc(chatId)
          .collection('messages')
          .doc(groupMessageId)
          .set(groupMessage.toMap());

      await _updateChatLastMessage(chatId, groupMessage);

      // Get group members and process broadcast asynchronously
      final chatDoc = await _chatsCollection.doc(chatId).get();
      if (chatDoc.exists) {
        final chatData = chatDoc.data() as Map<String, dynamic>;
        final memberIds =
            List<String>.from(
              chatData['memberIds'] ?? [],
            ).where((id) => id != currentUserId.toString()).toList();

        // Process broadcast to members asynchronously for better performance
        _processBroadcastAsync(
          memberIds: memberIds,
          text: text,
          currentUserId: currentUserId.toString(),
          currentUserName: currentUserName,
          replyToMessageId: replyToMessageId,
          replyToText: replyToText,
          replyToSenderName: replyToSenderName,
        );
      }
    } catch (e) {
      throw Exception('Failed to send broadcast message: $e');
    }
  }

  /// Process broadcast to members asynchronously
  static Future<void> _processBroadcastAsync({
    required List<String> memberIds,
    required String text,
    required String currentUserId,
    required String currentUserName,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
  }) async {
    try {
      // Process members in parallel for better performance
      final futures = memberIds.map((memberId) async {
        try {
          // Get or create individual chat with this member
          final individualChatId = await _getOrCreateIndividualChatForBroadcast(
            memberId,
          );

          // Send message to individual chat
          final messageId = _generateMessageId();
          final message = Message(
            id: messageId,
            senderId: currentUserId,
            senderName: currentUserName,
            type: MessageType.text,
            text: text,
            timestamp: DateTime.now(),
            replyToMessageId: replyToMessageId,
            replyToText: replyToText,
            replyToSenderName: replyToSenderName,
            status: MessageStatus.sent,
          );

          // Add message to individual chat
          await _chatsCollection
              .doc(individualChatId)
              .collection('messages')
              .doc(messageId)
              .set(message.toMap());

          // Update individual chat's last message and unread count in parallel
          await Future.wait([
            _updateChatLastMessage(individualChatId, message),
            _chatsCollection.doc(individualChatId).update({
              'unreadCounts.$memberId': FieldValue.increment(1),
            }),
          ]);
        } catch (e) {
          // Log error but don't fail the entire broadcast
          // print('Failed to send message to member $memberId: $e');
        }
      });

      // Wait for all broadcasts to complete
      await Future.wait(futures);
    } catch (e) {
      // print('Error in broadcast processing: $e');
    }
  }

  /// Get messages stream for a chat
  static Stream<List<Message>> getMessagesStream(String chatId) async* {
    try {
      // Ensure Firebase Auth is properly initialized before accessing messages
      await syncCurrentUserWithFirebase();

      // Add a small delay to ensure auth is fully established
      await Future.delayed(const Duration(milliseconds: 100));

      yield* _chatsCollection
          .doc(chatId)
          .collection('messages')
          .orderBy('timestamp', descending: true)
          .snapshots()
          .map(
            (snapshot) =>
                snapshot.docs.map((doc) => Message.fromDocument(doc)).toList(),
          );
    } catch (e) {
      // If there's an auth error, try to reinitialize and retry
      try {
        await switchUser();
        await Future.delayed(const Duration(milliseconds: 500));

        yield* _chatsCollection
            .doc(chatId)
            .collection('messages')
            .orderBy('timestamp', descending: true)
            .snapshots()
            .map(
              (snapshot) =>
                  snapshot.docs
                      .map((doc) => Message.fromDocument(doc))
                      .toList(),
            );
      } catch (e2) {
        // If still failing, yield empty list to prevent app crash
        yield [];
      }
    }
  }

  // ==================== SESSION MANAGEMENT ====================

  /// Sign out from Firebase Auth (call this when user logs out from Laravel)
  static Future<void> signOutFromFirebase() async {
    try {
      if (_auth.currentUser != null) {
        await _auth.signOut();
      }
    } catch (e) {
      // Silently handle sign out errors
    }
  }

  /// Clear all Firebase Auth and reinitialize for new user
  static Future<void> switchUser() async {
    try {
      // Sign out current Firebase user
      await signOutFromFirebase();

      // Clear any cached data
      await Future.delayed(const Duration(milliseconds: 500));

      // Reinitialize for new user
      await syncCurrentUserWithFirebase();
    } catch (e) {
      // Silently handle user switch errors
    }
  }

  /// Force refresh chat providers (call this after user login/logout)
  static void refreshChatProviders() {
    // This method can be called to trigger provider refresh
    // The autoDispose providers will automatically refresh when accessed
  }

  // ==================== HELPER METHODS ====================

  /// Generate chat ID from two user IDs
  static String _generateChatId(String userId1, String userId2) {
    final sortedIds = [userId1, userId2]..sort();
    return '${sortedIds[0]}_${sortedIds[1]}';
  }

  /// Generate unique message ID
  static String _generateMessageId() {
    return DateTime.now().millisecondsSinceEpoch.toString() +
        Random().nextInt(1000).toString();
  }

  /// Update message status
  static Future<void> _updateMessageStatus(
    String chatId,
    String messageId,
    MessageStatus status,
  ) async {
    try {
      await _chatsCollection
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .update({
            'status': status.name,
            'updatedAt': DateTime.now().millisecondsSinceEpoch,
          });
    } catch (e) {
      throw Exception('Failed to update message status: $e');
    }
  }

  /// Update chat's last message
  static Future<void> _updateChatLastMessage(
    String chatId,
    Message message,
  ) async {
    try {
      await _chatsCollection.doc(chatId).update({
        'lastMessage': message.toMap(),
        'lastMessageTime': message.timestamp.millisecondsSinceEpoch,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      throw Exception('Failed to update chat last message: $e');
    }
  }

  /// Send image message
  static Future<void> sendImageMessage({
    required String chatId,
    required File imageFile,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
    DateTime? timestampOverride,
    Map<String, dynamic>? extraMetadata,
  }) async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();
      final currentUserRole = await SessionService.getUserRole();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      // Upload image to Firebase Storage
      String? imageUrl;
      try {
        imageUrl = await _uploadFile(imageFile, 'images');
      } catch (e) {
        throw Exception('Failed to upload image: $e');
      }

      // Check if this is a group chat and user is admin
      final chatDoc = await _chatsCollection.doc(chatId).get();
      if (chatDoc.exists) {
        final chatData = chatDoc.data() as Map<String, dynamic>;
        final chatType = chatData['type'] ?? 'individual';

        // If it's a group chat and user is admin, use broadcast messaging
        if (chatType == 'group' && currentUserRole == 'admin') {
          await _sendBroadcastImageMessage(
            chatId: chatId,
            imageUrl: imageUrl,
            replyToMessageId: replyToMessageId,
            replyToText: replyToText,
            replyToSenderName: replyToSenderName,
          );
          return;
        }
      }

      // Regular individual chat image message
      final messageId = _generateMessageId();
      final message = Message(
        id: messageId,
        senderId: currentUserId.toString(),
        senderName: currentUserName,
        type: MessageType.image,
        mediaUrl: imageUrl,
        timestamp: timestampOverride ?? DateTime.now(),
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
        status: MessageStatus.sending,
        metadata: extraMetadata,
      );

      // Add message to chat's messages subcollection
      await _chatsCollection
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .set(message.toMap());

      // Update message status to sent
      await _updateMessageStatus(chatId, messageId, MessageStatus.sent);

      // Update chat's last message
      await _updateChatLastMessage(
        chatId,
        message.copyWith(status: MessageStatus.sent),
      );

      // Update unreadCounts for all other members and send notifications
      final chatDocForUnread = await _chatsCollection.doc(chatId).get();
      if (chatDocForUnread.exists) {
        final chatDataForUnread =
            chatDocForUnread.data() as Map<String, dynamic>;
        final memberIds = List<String>.from(
          chatDataForUnread['memberIds'] ?? [],
        );
        final unreadCounts = Map<String, int>.from(
          chatDataForUnread['unreadCounts'] ?? {},
        );
        for (final memberId in memberIds) {
          if (memberId != currentUserId.toString()) {
            unreadCounts[memberId] = (unreadCounts[memberId] ?? 0) + 1;
          }
        }
        await _chatsCollection.doc(chatId).update({
          'unreadCounts': unreadCounts,
        });

        // Send push notifications to other members
        await _sendNotificationToMembers(
          memberIds:
              memberIds.where((id) => id != currentUserId.toString()).toList(),
          chatId: chatId,
          senderName: currentUserName,
          messageText: '📷 Photo',
          messageType: 'image',
        );
      }
    } catch (e) {
      throw Exception('Failed to send image message: $e');
    }
  }

  /// Send broadcast image message to group members as individual messages
  static Future<void> _sendBroadcastImageMessage({
    required String chatId,
    required String imageUrl,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
    DateTime? timestampOverride,
    Map<String, dynamic>? extraMetadata,
  }) async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      // First, add the image message to the group chat immediately for admin to see
      final groupMessageId = _generateMessageId();
      final groupMessage = Message(
        id: groupMessageId,
        senderId: currentUserId.toString(),
        senderName: currentUserName,
        type: MessageType.image,
        mediaUrl: imageUrl,
        timestamp: timestampOverride ?? DateTime.now(),
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
        status: MessageStatus.sent,
        metadata: extraMetadata,
      );

      await _chatsCollection
          .doc(chatId)
          .collection('messages')
          .doc(groupMessageId)
          .set(groupMessage.toMap());

      await _updateChatLastMessage(chatId, groupMessage);

      // Get group members and process broadcast asynchronously
      final chatDoc = await _chatsCollection.doc(chatId).get();
      if (chatDoc.exists) {
        final chatData = chatDoc.data() as Map<String, dynamic>;
        final memberIds =
            List<String>.from(
              chatData['memberIds'] ?? [],
            ).where((id) => id != currentUserId.toString()).toList();

        // Process image broadcast to members asynchronously
        _processImageBroadcastAsync(
          memberIds: memberIds,
          imageUrl: imageUrl,
          currentUserId: currentUserId.toString(),
          currentUserName: currentUserName,
          replyToMessageId: replyToMessageId,
          replyToText: replyToText,
          replyToSenderName: replyToSenderName,
        );
      }
    } catch (e) {
      throw Exception('Failed to send broadcast image message: $e');
    }
  }

  /// Process image broadcast to members asynchronously
  static Future<void> _processImageBroadcastAsync({
    required List<String> memberIds,
    required String imageUrl,
    required String currentUserId,
    required String currentUserName,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
  }) async {
    try {
      // Process members in parallel for better performance
      final futures = memberIds.map((memberId) async {
        try {
          // Get or create individual chat with this member
          final individualChatId = await _getOrCreateIndividualChatForBroadcast(
            memberId,
          );

          // Send image message to individual chat
          final messageId = _generateMessageId();
          final message = Message(
            id: messageId,
            senderId: currentUserId,
            senderName: currentUserName,
            type: MessageType.image,
            mediaUrl: imageUrl,
            timestamp: DateTime.now(),
            replyToMessageId: replyToMessageId,
            replyToText: replyToText,
            replyToSenderName: replyToSenderName,
            status: MessageStatus.sent,
          );

          // Add message to individual chat
          await _chatsCollection
              .doc(individualChatId)
              .collection('messages')
              .doc(messageId)
              .set(message.toMap());

          // Update individual chat's last message and unread count in parallel
          await Future.wait([
            _updateChatLastMessage(individualChatId, message),
            _chatsCollection.doc(individualChatId).update({
              'unreadCounts.$memberId': FieldValue.increment(1),
            }),
          ]);
        } catch (e) {
          // Log error but don't fail the entire broadcast
          // print('Failed to send image to member $memberId: $e');
        }
      });

      // Wait for all broadcasts to complete
      await Future.wait(futures);
    } catch (e) {
      // print('Error in image broadcast processing: $e');
    }
  }

  /// Send file message
  static Future<void> sendFileMessage({
    required String chatId,
    required File file,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
  }) async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      // Upload file to Firebase Storage
      String? fileUrl;
      try {
        fileUrl = await _uploadFile(file, 'files');
      } catch (e) {
        throw Exception('Failed to upload file: $e');
      }
      final fileName = file.path.split('/').last;
      final fileSize = await file.length();

      final messageId = _generateMessageId();
      final message = Message(
        id: messageId,
        senderId: currentUserId.toString(),
        senderName: currentUserName,
        type: MessageType.file,
        mediaUrl: fileUrl,
        fileName: fileName,
        fileSize: fileSize,
        timestamp: DateTime.now(),
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
        status: MessageStatus.sending,
      );

      // Add message to chat's messages subcollection
      await _chatsCollection
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .set(message.toMap());

      // Update message status to sent
      await _updateMessageStatus(chatId, messageId, MessageStatus.sent);

      // Update chat's last message
      await _updateChatLastMessage(
        chatId,
        message.copyWith(status: MessageStatus.sent),
      );
    } catch (e) {
      throw Exception('Failed to send file message: $e');
    }
  }

  /// Send single catalog message
  static Future<void> sendSingleCatalogMessage({
    required String chatId,
    required Map<String, dynamic> catalog,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
  }) async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();
      final currentUserRole = await SessionService.getUserRole();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      // Check if this is a group chat and user is admin
      final chatDoc = await _chatsCollection.doc(chatId).get();
      if (chatDoc.exists) {
        final chatData = chatDoc.data() as Map<String, dynamic>;
        final chatType = chatData['type'] ?? 'individual';

        // If it's a group chat and user is admin, use broadcast messaging
        if (chatType == 'group' && currentUserRole == 'admin') {
          // First send to the group chat
          final groupMessageId = _generateMessageId();
          final groupMessage = Message(
            id: groupMessageId,
            senderId: currentUserId.toString(),
            senderName: currentUserName,
            type: MessageType.catalog,
            text: 'Catalog shared',
            timestamp: DateTime.now(),
            replyToMessageId: replyToMessageId,
            replyToText: replyToText,
            replyToSenderName: replyToSenderName,
            status: MessageStatus.sent,
            metadata: {
              'catalogs': [catalog],
              'count': 1,
            },
          );

          await _chatsCollection
              .doc(chatId)
              .collection('messages')
              .doc(groupMessageId)
              .set(groupMessage.toMap());

          await _updateChatLastMessage(chatId, groupMessage);

          // Get member IDs for broadcasting
          final memberIds =
              List<String>.from(
                chatData['memberIds'] ?? [],
              ).where((id) => id != currentUserId.toString()).toList();

          // Process broadcast to members asynchronously
          final futures = memberIds.map((memberId) async {
            try {
              final individualChatId =
                  await _getOrCreateIndividualChatForBroadcast(memberId);
              final messageId = _generateMessageId();
              final message = Message(
                id: messageId,
                senderId: currentUserId.toString(),
                senderName: currentUserName,
                type: MessageType.catalog,
                text: 'Catalog shared',
                timestamp: DateTime.now(),
                replyToMessageId: replyToMessageId,
                replyToText: replyToText,
                replyToSenderName: replyToSenderName,
                status: MessageStatus.sent,
                metadata: {
                  'catalogs': [catalog],
                  'count': 1,
                },
              );

              await _chatsCollection
                  .doc(individualChatId)
                  .collection('messages')
                  .doc(messageId)
                  .set(message.toMap());

              await Future.wait([
                _updateChatLastMessage(individualChatId, message),
                _chatsCollection.doc(individualChatId).update({
                  'unreadCounts.$memberId': FieldValue.increment(1),
                }),
              ]);
            } catch (e) {
              // print('Failed to send catalog to member $memberId: $e');
            }
          });

          await Future.wait(futures);
          return;
        }
      }

      final messageId = _generateMessageId();
      final message = Message(
        id: messageId,
        senderId: currentUserId.toString(),
        senderName: currentUserName,
        type: MessageType.catalog,
        text: 'Catalog shared',
        timestamp: DateTime.now(),
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
        status: MessageStatus.sending,
        metadata: {
          'catalogs': [catalog],
          'count': 1,
        },
      );

      // Add message to chat's messages subcollection
      await _chatsCollection
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .set(message.toMap());

      // Update message status to sent
      await _updateMessageStatus(chatId, messageId, MessageStatus.sent);

      // Update chat's last message
      await _updateChatLastMessage(
        chatId,
        message.copyWith(status: MessageStatus.sent),
      );

      // Update unreadCounts for all other members
      final chatDocForUnread = await _chatsCollection.doc(chatId).get();
      if (chatDocForUnread.exists) {
        final chatDataForUnread =
            chatDocForUnread.data() as Map<String, dynamic>;
        final memberIds = List<String>.from(
          chatDataForUnread['memberIds'] ?? [],
        );
        final unreadCounts = Map<String, int>.from(
          chatDataForUnread['unreadCounts'] ?? {},
        );

        for (final memberId in memberIds) {
          if (memberId != currentUserId.toString()) {
            unreadCounts[memberId] = (unreadCounts[memberId] ?? 0) + 1;
          }
        }

        await _chatsCollection.doc(chatId).update({
          'unreadCounts': unreadCounts,
        });
      }
    } catch (e) {
      throw Exception('Failed to send catalog message: $e');
    }
  }

  /// Send catalog message (multiple catalogs - kept for backward compatibility)
  static Future<void> sendCatalogMessage({
    required String chatId,
    required List<Map<String, dynamic>> catalogs,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
  }) async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      final messageId = _generateMessageId();
      final message = Message(
        id: messageId,
        senderId: currentUserId.toString(),
        senderName: currentUserName,
        type: MessageType.catalog,
        text:
            '${catalogs.length} catalog${catalogs.length == 1 ? '' : 's'} shared',
        timestamp: DateTime.now(),
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
        status: MessageStatus.sending,
        metadata: {'catalogs': catalogs, 'count': catalogs.length},
      );

      // Add message to chat's messages subcollection
      await _chatsCollection
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .set(message.toMap());

      // Update message status to sent
      await _updateMessageStatus(chatId, messageId, MessageStatus.sent);

      // Update chat's last message
      await _updateChatLastMessage(
        chatId,
        message.copyWith(status: MessageStatus.sent),
      );
    } catch (e) {
      throw Exception('Failed to send catalog message: $e');
    }
  }

  /// Ensure Firebase Auth user exists with Laravel user ID as UID
  static Future<void> _ensureFirebaseAuthWithLaravelId(
    String laravelUserId,
  ) async {
    try {
      // Check if current Firebase user is already mapped to this Laravel user
      if (_auth.currentUser != null) {
        final userDoc = await _usersCollection.doc(laravelUserId).get();
        if (userDoc.exists) {
          final data = userDoc.data() as Map<String, dynamic>?;
          if (data?['firebaseUid'] == _auth.currentUser!.uid) {
            // Already properly mapped, no need to re-authenticate
            return;
          }
        }
        // Different user, sign out first
        await _auth.signOut();
      }

      // Sign in anonymously to get a Firebase UID
      final userCredential = await _auth.signInAnonymously();

      if (userCredential.user != null) {
        // Create user document with Laravel user ID as document ID
        // and store the Firebase UID for mapping
        await _usersCollection.doc(laravelUserId).set({
          'id': laravelUserId,
          'firebaseUid': userCredential.user!.uid,
          'laravelUserId': laravelUserId,
          'updatedAt': DateTime.now().millisecondsSinceEpoch,
        }, SetOptions(merge: true));

        // Also create a reverse mapping for the security rules
        await _usersCollection.doc(userCredential.user!.uid).set({
          'id': userCredential.user!.uid,
          'firebaseUid': userCredential.user!.uid,
          'laravelUserId': laravelUserId,
          'updatedAt': DateTime.now().millisecondsSinceEpoch,
        }, SetOptions(merge: true));
      }
    } catch (e) {
      // Silently handle Firebase auth errors
    }
  }

  /// Ensure Firebase Auth is authenticated
  static Future<void> _ensureFirebaseAuth() async {
    try {
      // Check if already authenticated
      if (_auth.currentUser != null) {
        return;
      }

      // Try to sign in anonymously for Firebase Storage access
      try {
        await _auth.signInAnonymously();
      } catch (e) {
        // If anonymous auth fails, continue without auth
      }
    } catch (e) {
      // Silently handle auth initialization errors
    }
  }

  /// Public method to upload file to storage
  static Future<String> uploadFile(File file, String folder) async {
    return _uploadFile(file, folder);
  }

  /// Upload file to storage (Firebase Storage or Laravel backend)
  static Future<String> _uploadFile(File file, String folder) async {
    try {
      // Always ensure proper file path format
      final normalizedFilePath = file.path.replaceAll('\\', '/');
      if (normalizedFilePath.startsWith('file://')) {
        file = File(normalizedFilePath.replaceFirst('file://', ''));
      }

      // Try Firebase Storage first
      await _ensureFirebaseAuth();

      if (_auth.currentUser != null) {
        // Try Firebase upload first
        try {
          return await _uploadToFirebaseStorage(file, folder);
        } catch (firebaseError) {
          // print('Firebase upload failed: $firebaseError');
          // Continue to Laravel fallback
        }
      }

      // Fallback to Laravel backend storage
      try {
        return await _uploadToLaravelBackend(file, folder);
      } catch (backendError) {
        // print('Laravel upload failed: $backendError');
        // If both fail, try one last time with Firebase
        if (_auth.currentUser != null) {
          return await _uploadToFirebaseStorage(file, folder);
        }
        throw backendError;
      }
    } catch (e) {
      throw Exception('Failed to upload file: $e');
    }
  }

  /// Upload file to Firebase Storage
  static Future<String> _uploadToFirebaseStorage(
    File file,
    String folder,
  ) async {
    try {
      // Ensure user is authenticated
      if (_auth.currentUser == null) {
        await _auth.signInAnonymously();
      }

      // Ensure file exists and is accessible
      if (!await file.exists()) {
        throw Exception('File does not exist: ${file.path}');
      }

      // Get file name without any problematic schemes
      final normalizedPath = file.path.replaceAll('\\', '/');
      final fileName =
          '${DateTime.now().millisecondsSinceEpoch}_${normalizedPath.split('/').last}';

      final ref = _storage.ref().child('$folder/$fileName');

      // Add metadata for better file handling
      final metadata = SettableMetadata(
        contentType: _getContentType(file.path),
        customMetadata: {
          'uploadedBy': _auth.currentUser?.uid ?? 'anonymous',
          'uploadedAt': DateTime.now().toIso8601String(),
          'originalFileName': fileName,
        },
      );

      // Upload with retries
      int retryCount = 0;
      const maxRetries = 3;
      String? downloadUrl;

      while (retryCount < maxRetries && downloadUrl == null) {
        try {
          final uploadTask = ref.putFile(file, metadata);
          final snapshot = await uploadTask;
          downloadUrl = await snapshot.ref.getDownloadURL();
        } catch (e) {
          retryCount++;
          if (retryCount >= maxRetries) {
            throw e;
          }
          await Future.delayed(
            Duration(seconds: retryCount),
          ); // Exponential backoff
        }
      }

      if (downloadUrl == null) {
        throw Exception(
          'Failed to get download URL after $maxRetries attempts',
        );
      }

      return downloadUrl;
    } catch (e) {
      throw Exception('Failed to upload file to Firebase Storage: $e');
    }
  }

  /// Stream typing status for a chat (userId -> isTyping)
  static Stream<Map<String, bool>> getTypingStatusStream(String chatId) {
    return _chatsCollection.doc(chatId).snapshots().map((doc) {
      final data = doc.data() as Map<String, dynamic>?;
      if (data == null || data['typingStatus'] == null) return {};
      final Map<String, dynamic> typing = data['typingStatus'];
      return typing.map((k, v) => MapEntry(k, v == true));
    });
  }

  /// Set typing status for a user in a chat
  static Future<void> setTypingStatus(
    String chatId,
    String userId,
    bool isTyping,
  ) async {
    await _chatsCollection.doc(chatId).set({
      'typingStatus': {userId: isTyping},
    }, SetOptions(merge: true));
  }

  /// Get content type based on file extension
  static String _getContentType(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      default:
        return 'application/octet-stream';
    }
  }

  /// Upload file to Laravel backend
  static Future<String> _uploadToLaravelBackend(
    File file,
    String folder,
  ) async {
    try {
      // Ensure file exists and is accessible
      if (!await file.exists()) {
        throw Exception('File does not exist: ${file.path}');
      }

      // Get normalized file name
      final normalizedPath = file.path.replaceAll('\\', '/');
      final fileName = normalizedPath.split('/').last;
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final uniqueFileName = '${timestamp}_$fileName';

      // For now, try Firebase Storage as fallback since Laravel
      // backend file upload is not yet implemented
      int retryCount = 0;
      const maxRetries = 3;
      String? uploadedUrl;

      while (retryCount < maxRetries && uploadedUrl == null) {
        try {
          final ref = _storage.ref().child('$folder/$uniqueFileName');
          final metadata = SettableMetadata(
            contentType: _getContentType(file.path),
            customMetadata: {
              'uploadedBy': 'laravel_backend',
              'uploadedAt': DateTime.now().toIso8601String(),
              'originalFileName': fileName,
            },
          );

          final uploadTask = ref.putFile(file, metadata);
          final snapshot = await uploadTask;
          uploadedUrl = await snapshot.ref.getDownloadURL();
        } catch (e) {
          retryCount++;
          if (retryCount >= maxRetries) {
            // Only throw on last retry
            rethrow;
          }
          await Future.delayed(
            Duration(seconds: retryCount),
          ); // Exponential backoff
        }
      }

      if (uploadedUrl != null) {
        return uploadedUrl;
      }

      // If all retries fail, return Laravel backend URL format
      // This URL should be handled by the Laravel backend once implemented
      final backendUrl =
          'https://mrgarment.braincavesoft.com/storage/$folder/$uniqueFileName';

      // Verify URL is valid
      try {
        final response = await http.head(Uri.parse(backendUrl));
        if (response.statusCode == 200) {
          return backendUrl;
        }
        throw Exception('Backend URL not accessible');
      } catch (e) {
        throw Exception('Invalid backend URL: $backendUrl');
      }
    } catch (e) {
      throw Exception('Failed to upload file to Laravel backend: $e');
    }
  }

  /// Mark message as read
  static Future<void> markMessageAsRead(
    String chatId,
    String messageId,
    String userId,
  ) async {
    try {
      await _chatsCollection
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .update({
            'readBy': FieldValue.arrayUnion([userId]),
            'status': MessageStatus.read.name,
          });
    } catch (e) {
      throw Exception('Failed to mark message as read: $e');
    }
  }

  /// Mark all messages as read for a user
  static Future<void> markAllMessagesAsRead(
    String chatId,
    String userId,
  ) async {
    try {
      final messagesQuery =
          await _chatsCollection
              .doc(chatId)
              .collection('messages')
              .where('senderId', isNotEqualTo: userId)
              .get();

      final batch = _firestore.batch();
      for (final doc in messagesQuery.docs) {
        final messageData = doc.data();
        final readBy = List<String>.from(messageData['readBy'] ?? []);

        if (!readBy.contains(userId)) {
          batch.update(doc.reference, {
            'readBy': FieldValue.arrayUnion([userId]),
            'status': MessageStatus.read.name,
          });
        }
      }

      await batch.commit();
      // Set unread count to 0 for this user in the chat document
      await _chatsCollection.doc(chatId).update({'unreadCounts.$userId': 0});
    } catch (e) {
      throw Exception('Failed to mark all messages as read: $e');
    }
  }

  /// Delete message - handles both admin (complete deletion) and user (soft deletion)
  static Future<void> deleteMessage(String chatId, String messageId) async {
    try {
      // Get current user info
      final currentUserId = await SessionService.getUserId();
      final currentUserRole = await SessionService.getUserRole();

      if (currentUserId == null) {
        throw Exception('User not logged in');
      }

      // If user is admin, delete the message completely (current behavior)
      if (currentUserRole == 'admin') {
        await _deleteMessageCompletely(chatId, messageId);
      } else {
        // For non-admin users, mark message as deleted for this user only
        await _deleteMessageForUser(
          chatId,
          messageId,
          currentUserId.toString(),
        );
      }
    } catch (e) {
      throw Exception('Failed to delete message: $e');
    }
  }

  /// Completely delete message (admin only)
  static Future<void> _deleteMessageCompletely(
    String chatId,
    String messageId,
  ) async {
    try {
      // Get the chat document to check if this is the last message
      final chatDoc = await _chatsCollection.doc(chatId).get();
      final chatData = chatDoc.data() as Map<String, dynamic>?;

      // Check if the message being deleted is the last message
      final lastMessageData = chatData?['lastMessage'] as Map<String, dynamic>?;
      final isLastMessage = lastMessageData?['id'] == messageId;

      // Delete the message
      await _chatsCollection
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .delete();

      // If this was the last message, update the chat's last message
      if (isLastMessage) {
        await _updateChatLastMessageAfterDeletion(chatId);
      }
    } catch (e) {
      throw Exception('Failed to delete message completely: $e');
    }
  }

  /// Mark message as deleted for specific user (soft deletion)
  static Future<void> _deleteMessageForUser(
    String chatId,
    String messageId,
    String userId,
  ) async {
    try {
      // Get the message document
      final messageDoc =
          await _chatsCollection
              .doc(chatId)
              .collection('messages')
              .doc(messageId)
              .get();

      if (!messageDoc.exists) {
        throw Exception('Message not found');
      }

      final messageData = messageDoc.data() as Map<String, dynamic>;
      final deletedBy = List<String>.from(messageData['deletedBy'] ?? []);

      // Add user to deletedBy list if not already present
      if (!deletedBy.contains(userId)) {
        deletedBy.add(userId);

        // Update the message with the new deletedBy list
        await _chatsCollection
            .doc(chatId)
            .collection('messages')
            .doc(messageId)
            .update({'deletedBy': deletedBy});
      }
    } catch (e) {
      throw Exception('Failed to delete message for user: $e');
    }
  }

  /// Update chat's last message after a message deletion
  static Future<void> _updateChatLastMessageAfterDeletion(String chatId) async {
    try {
      // Get the most recent message (excluding the deleted one)
      final messagesQuery =
          await _chatsCollection
              .doc(chatId)
              .collection('messages')
              .orderBy('timestamp', descending: true)
              .limit(1)
              .get();

      if (messagesQuery.docs.isNotEmpty) {
        // Update with the new last message
        final lastMessage = Message.fromDocument(messagesQuery.docs.first);
        await _updateChatLastMessage(chatId, lastMessage);
      } else {
        // No messages left, clear the last message
        await _chatsCollection.doc(chatId).update({
          'lastMessage': null,
          'lastMessageTime': null,
          'updatedAt': DateTime.now().millisecondsSinceEpoch,
        });
      }
    } catch (e) {
      throw Exception('Failed to update last message after deletion: $e');
    }
  }

  /// Forward message
  static Future<void> forwardMessage({
    required String fromChatId,
    required String messageId,
    required List<String> toChatIds,
  }) async {
    try {
      // Get original message
      final messageDoc =
          await _chatsCollection
              .doc(fromChatId)
              .collection('messages')
              .doc(messageId)
              .get();

      if (!messageDoc.exists) throw Exception('Message not found');

      final originalMessage = Message.fromDocument(messageDoc);
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();
      final currentUserRole = await SessionService.getUserRole();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      // Forward to each chat
      for (final chatId in toChatIds) {
        final chatDoc = await _chatsCollection.doc(chatId).get();
        if (!chatDoc.exists) continue;

        final chatData = chatDoc.data() as Map<String, dynamic>;
        final chatType = chatData['type'] as String?;

        // For group chats where user is admin, use broadcast forwarding
        if (chatType == 'group' && currentUserRole == 'admin') {
          // First, add the message to the group chat for admin to see
          final groupMessageId = _generateMessageId();
          final forwardedGroupMessage = originalMessage.copyWith(
            id: groupMessageId,
            senderId: currentUserId.toString(),
            senderName: currentUserName,
            timestamp: DateTime.now(),
            isForwarded: true,
            status: MessageStatus.sent,
            replyToMessageId: null,
          );

          await _chatsCollection
              .doc(chatId)
              .collection('messages')
              .doc(groupMessageId)
              .set(forwardedGroupMessage.toMap());

          await _updateChatLastMessage(chatId, forwardedGroupMessage);

          // Get member IDs for broadcasting (excluding the admin)
          final memberIds =
              List<String>.from(
                chatData['memberIds'] ?? [],
              ).where((id) => id != currentUserId.toString()).toList();

          // Broadcast to each member in their individual chat
          for (final memberId in memberIds) {
            try {
              // Get or create individual chat with this member
              final individualChatId =
                  await _getOrCreateIndividualChatForBroadcast(memberId);

              // Forward message to individual chat
              final individualMessageId = _generateMessageId();
              final forwardedIndividualMessage = originalMessage.copyWith(
                id: individualMessageId,
                senderId: currentUserId.toString(),
                senderName: currentUserName,
                timestamp: DateTime.now(),
                isForwarded: true,
                status: MessageStatus.sent,
                replyToMessageId: null,
              );

              await _chatsCollection
                  .doc(individualChatId)
                  .collection('messages')
                  .doc(individualMessageId)
                  .set(forwardedIndividualMessage.toMap());

              await Future.wait([
                _updateChatLastMessage(
                  individualChatId,
                  forwardedIndividualMessage,
                ),
                _chatsCollection.doc(individualChatId).update({
                  'unreadCounts.$memberId': FieldValue.increment(1),
                }),
              ]);
            } catch (e) {
              // print('Failed to forward message to member $memberId: $e');
              continue;
            }
          }
        } else {
          // Regular individual chat forwarding
          final newMessageId = _generateMessageId();
          final forwardedMessage = originalMessage.copyWith(
            id: newMessageId,
            senderId: currentUserId.toString(),
            senderName: currentUserName,
            timestamp: DateTime.now(),
            isForwarded: true,
            status: MessageStatus.sent,
            replyToMessageId: null,
          );

          final memberIds = List<String>.from(chatData['memberIds'] ?? []);
          final unreadCounts = Map<String, int>.from(
            chatData['unreadCounts'] ?? {},
          );

          // Increment unread count for all members except the sender
          for (final memberId in memberIds) {
            if (memberId != currentUserId.toString()) {
              unreadCounts[memberId] = (unreadCounts[memberId] ?? 0) + 1;
            }
          }
          await _chatsCollection.doc(chatId).update({
            'unreadCounts': unreadCounts,
          });

          await _chatsCollection
              .doc(chatId)
              .collection('messages')
              .doc(newMessageId)
              .set(forwardedMessage.toMap());

          await _updateChatLastMessage(chatId, forwardedMessage);
        }
      }
    } catch (e) {
      throw Exception('Failed to forward message: $e');
    }
  }

  // ==================== GROUP MANAGEMENT ====================

  /// Create group
  static Future<String> createGroup({
    required String groupName,
    String? description,
    required List<String> memberIds,
    File? groupImage,
  }) async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      final groupId = _generateGroupId();
      String? groupImageUrl;

      // Upload group image if provided
      if (groupImage != null) {
        try {
          groupImageUrl = await _uploadFile(groupImage, 'group_images');
        } catch (e) {
          // If image upload fails, continue without image
          groupImageUrl = null;
        }
      }

      // Get member details
      final members = <GroupMember>[];
      final memberNames = <String, String>{};
      final memberProfileUrls = <String, String?>{};

      // Ensure current user is synced to Firebase
      await syncCurrentUserWithFirebase();

      // Add creator as admin
      ChatUser? creator = await getUser(currentUserId.toString());
      if (creator == null) {
        // If creator not found in Firebase, create from session data
        // Note: FCM token will be null here, but it should be set separately via FCM initialization
        creator = ChatUser(
          id: currentUserId.toString(),
          name: currentUserName,
          email: await SessionService.getUserEmail() ?? '',
          role: await SessionService.getUserRole() ?? 'user',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        await createOrUpdateUser(creator);
      }

      members.add(
        GroupMember(
          userId: currentUserId.toString(),
          name: creator.name,
          profileUrl: creator.profileImageUrl,
          role: 'admin',
          joinedAt: DateTime.now(),
        ),
      );
      memberNames[currentUserId.toString()] = creator.name;
      memberProfileUrls[currentUserId.toString()] = creator.profileImageUrl;

      // Add other members
      for (final memberId in memberIds) {
        if (memberId != currentUserId.toString()) {
          ChatUser? user = await getUser(memberId);

          // If user not found in Firebase, try to get from Laravel backend
          if (user == null) {
            try {
              final apiUsers = await UserService.fetchAllUsers();
              final userData = apiUsers.firstWhere(
                (u) => u['id'].toString() == memberId,
                orElse: () => null,
              );

              if (userData != null) {
                user = ChatUser(
                  id: userData['id'].toString(),
                  name: userData['name'] ?? 'Unknown User',
                  email: userData['email'] ?? '',
                  role:
                      userData['account_type']?.toString().toLowerCase() ??
                      'user',
                  profileImageUrl: userData['profile_image_url'],
                  createdAt: DateTime.now(),
                  updatedAt: DateTime.now(),
                );

                // Sync to Firebase
                await createOrUpdateUser(user);
              }
            } catch (e) {
              // Silently handle member fetch errors
            }
          }

          if (user != null) {
            members.add(
              GroupMember(
                userId: memberId,
                name: user.name,
                profileUrl: user.profileImageUrl,
                joinedAt: DateTime.now(),
              ),
            );
            memberNames[memberId] = user.name;
            memberProfileUrls[memberId] = user.profileImageUrl;
          }
        }
      }

      // Create group
      final group = Group(
        id: groupId,
        name: groupName,
        description: description,
        imageUrl: groupImageUrl,
        createdBy: currentUserId.toString(),
        members: members,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _groupsCollection.doc(groupId).set(group.toMap());

      // Create corresponding chat
      final chat = Chat(
        id: groupId,
        type: ChatType.group,
        memberIds: members.map((m) => m.userId).toList(),
        memberNames: memberNames,
        memberProfileUrls: memberProfileUrls,
        groupName: groupName,
        groupImageUrl: groupImageUrl,
        groupDescription: description,
        createdBy: currentUserId.toString(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _chatsCollection.doc(groupId).set(chat.toMap());

      return groupId;
    } catch (e) {
      throw Exception('Failed to create group: $e');
    }
  }

  /// Get user's groups
  static Future<List<Group>> getUserGroups(String userId) async {
    try {
      final querySnapshot =
          await _groupsCollection
              .where(
                'members',
                arrayContains: {'userId': userId, 'isActive': true},
              )
              .where('isActive', isEqualTo: true)
              .get();

      return querySnapshot.docs.map((doc) => Group.fromDocument(doc)).toList();
    } catch (e) {
      throw Exception('Failed to get user groups: $e');
    }
  }

  /// Get group by ID
  static Future<Group?> getGroup(String groupId) async {
    try {
      final doc = await _groupsCollection.doc(groupId).get();
      if (doc.exists) {
        return Group.fromDocument(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get group: $e');
    }
  }

  /// Add member to group
  static Future<void> addMemberToGroup(String groupId, String userId) async {
    try {
      final user = await getUser(userId);
      if (user == null) throw Exception('User not found');

      final newMember = GroupMember(
        userId: userId,
        name: user.name,
        profileUrl: user.profileImageUrl,
        joinedAt: DateTime.now(),
      );

      await _groupsCollection.doc(groupId).update({
        'members': FieldValue.arrayUnion([newMember.toMap()]),
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });

      // Update corresponding chat
      await _chatsCollection.doc(groupId).update({
        'memberIds': FieldValue.arrayUnion([userId]),
        'memberNames.$userId': user.name,
        'memberProfileUrls.$userId': user.profileImageUrl,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      throw Exception('Failed to add member to group: $e');
    }
  }

  /// Remove member from group
  static Future<void> removeMemberFromGroup(
    String groupId,
    String userId,
  ) async {
    try {
      // Get group to find the member
      final group = await getGroup(groupId);
      if (group == null) throw Exception('Group not found');

      final member = group.getMember(userId);
      if (member == null) throw Exception('Member not found');

      await _groupsCollection.doc(groupId).update({
        'members': FieldValue.arrayRemove([member.toMap()]),
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });

      // Update corresponding chat
      await _chatsCollection.doc(groupId).update({
        'memberIds': FieldValue.arrayRemove([userId]),
        'memberNames.$userId': FieldValue.delete(),
        'memberProfileUrls.$userId': FieldValue.delete(),
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      throw Exception('Failed to remove member from group: $e');
    }
  }

  // ==================== FCM MANAGEMENT ====================

  /// Initialize FCM
  static Future<void> initializeFCM() async {
    try {
      // Request permission
      final settings = await _messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        // Get FCM token
        final token = await _messaging.getToken();
        if (token != null) {
          final laravelUserId = await SessionService.getUserId();
          if (laravelUserId != null) {
            await updateFCMToken(laravelUserId.toString(), token);
          }
        }

        // Listen for token refresh
        _messaging.onTokenRefresh.listen((newToken) async {
          final currentUserId = await SessionService.getUserId();
          if (currentUserId != null) {
            await updateFCMToken(currentUserId.toString(), newToken);
          }
        });
      }
    } catch (e) {
      throw Exception('Failed to initialize FCM: $e');
    }
  }

  // ==================== HELPER METHODS ====================

  /// Generate unique group ID
  static String _generateGroupId() {
    return 'group_${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(1000)}';
  }

  /// Clear chat messages (keep chat but remove all messages)
  static Future<void> clearChat(String chatId) async {
    try {
      // Delete all messages in the chat
      final messagesSnapshot =
          await _chatsCollection.doc(chatId).collection('messages').get();

      final batch = _firestore.batch();
      for (final doc in messagesSnapshot.docs) {
        batch.delete(doc.reference);
      }
      await batch.commit();

      // Get current chat data to preserve user information
      final chatDoc = await _chatsCollection.doc(chatId).get();
      if (chatDoc.exists) {
        final chatData = chatDoc.data() as Map<String, dynamic>;

        // Reset unread counts for all members
        final memberIds = List<String>.from(chatData['memberIds'] ?? []);
        final resetUnreadCounts = <String, int>{};
        for (final memberId in memberIds) {
          resetUnreadCounts[memberId] = 0;
        }

        // Clear the last message and reset unread counts while preserving user info
        await _chatsCollection.doc(chatId).update({
          'lastMessage': null,
          'lastMessageTime': null,
          'unreadCounts': resetUnreadCounts,
          'updatedAt': DateTime.now().millisecondsSinceEpoch,
        });
      }
    } catch (e) {
      throw Exception('Failed to clear chat: $e');
    }
  }

  /// Delete chat - WhatsApp-like behavior (clears messages but keeps chat structure)
  static Future<void> deleteChat(String chatId) async {
    try {
      // Get chat info first to check if it's a group
      final chat = await getChat(chatId);

      if (chat?.isGroup == true) {
        // For groups, completely delete the chat and group
        // Delete all messages in the chat
        final messagesSnapshot =
            await _chatsCollection.doc(chatId).collection('messages').get();

        final batch = _firestore.batch();
        for (final doc in messagesSnapshot.docs) {
          batch.delete(doc.reference);
        }
        await batch.commit();

        // Delete the chat
        await _chatsCollection.doc(chatId).delete();

        // Delete the group
        await _groupsCollection.doc(chatId).delete();
      } else {
        // For individual chats, clear messages but keep chat structure
        // This allows users to chat again without losing user information
        await clearChat(chatId);
      }
    } catch (e) {
      throw Exception('Failed to delete chat: $e');
    }
  }

  /// Get unread message count for user
  static Future<int> getUnreadMessageCount(String userId) async {
    try {
      int totalUnread = 0;

      final chatsSnapshot =
          await _chatsCollection
              .where('memberIds', arrayContains: userId)
              .where('isActive', isEqualTo: true)
              .get();

      for (final chatDoc in chatsSnapshot.docs) {
        final chat = Chat.fromDocument(chatDoc);
        totalUnread += chat.getUnreadCount(userId);
      }

      return totalUnread;
    } catch (e) {
      throw Exception('Failed to get unread message count: $e');
    }
  }

  // ==================== NOTIFICATION METHODS ====================

  /// Send push notifications to chat members via backend
  static Future<void> _sendNotificationToMembers({
    required List<String> memberIds,
    required String chatId,
    required String senderName,
    required String messageText,
    required String messageType,
  }) async {
    try {
      // Ensure all members have FCM tokens before sending notifications
      await _ensureMembersHaveFCMTokens(memberIds);

      // Send notification request to backend
      await _sendNotificationViaBackend(
        memberIds: memberIds,
        chatId: chatId,
        senderName: senderName,
        messageText: messageText,
        messageType: messageType,
      );
    } catch (e) {
      debugPrint('Failed to send notifications: $e');
    }
  }

  /// Ensure all chat members have FCM tokens
  static Future<void> _ensureMembersHaveFCMTokens(
    List<String> memberIds,
  ) async {
    try {
      for (final memberId in memberIds) {
        final userDoc = await _usersCollection.doc(memberId).get();
        if (userDoc.exists) {
          final userData = userDoc.data() as Map<String, dynamic>;
          final fcmToken = userData['fcmToken'];

          if (fcmToken == null || fcmToken.toString().isEmpty) {
            debugPrint(
              '⚠️ User $memberId has no FCM token - they may not receive notifications',
            );
            // Note: We don't generate tokens here as that requires the user's device
            // The user needs to log in again or force update their token
          } else {
            debugPrint('✅ User $memberId has FCM token');
          }
        } else {
          debugPrint('⚠️ User $memberId not found in Firestore');
        }
      }
    } catch (e) {
      debugPrint('❌ Error checking member FCM tokens: $e');
    }
  }

  /// Send notification request to backend service
  static Future<void> _sendNotificationViaBackend({
    required List<String> memberIds,
    required String chatId,
    required String senderName,
    required String messageText,
    required String messageType,
  }) async {
    try {
      final currentUserId = await SessionService.getUserId();
      if (currentUserId == null) {
        debugPrint('❌ Current user ID is null, cannot send notification');
        return;
      }

      debugPrint('🔔 Sending notification to members: $memberIds');
      debugPrint('📱 Chat ID: $chatId');
      debugPrint('👤 Sender: $senderName');
      debugPrint('💬 Message: $messageText');

      // Firebase Cloud Function endpoint for sending notifications
      const backendUrl =
          'https://us-central1-mrgarments-f3b34.cloudfunctions.net/sendChatNotification';

      final payload = {
        'memberIds': memberIds,
        'chatId': chatId,
        'senderName': senderName,
        'messageText': messageText,
        'messageType': messageType,
        'senderId': currentUserId.toString(),
      };

      debugPrint('📤 Sending payload: ${jsonEncode(payload)}');

      final response = await http.post(
        Uri.parse(backendUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode(payload),
      );

      debugPrint('📥 Response status: ${response.statusCode}');
      debugPrint('📥 Response body: ${response.body}');

      if (response.statusCode != 200) {
        debugPrint(
          '❌ Failed to send notification via backend: ${response.body}',
        );
      } else {
        debugPrint('✅ Notification sent successfully via backend');
      }
    } catch (e) {
      debugPrint('❌ Error sending notification via backend: $e');
    }
  }
}
